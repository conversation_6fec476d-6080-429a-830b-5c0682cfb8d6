import { orpc } from "@/lib/orpc";

export const api = {
    // Authentication Routes (better-auth)
    auth: {
        login: "/auth/login",
        reqPasswordReset: "/auth/requestPasswordReset",
        passwordReset: "/auth/resetPassword",
        logout: "/auth/logout",
        // Registration Routes (ORPC)
        register: orpc.auth.register,
        checkUsername: orpc.auth.checkUsername,
    },

    // Admin Routes
    admin: {
        chatBan: orpc.admin.chatBanUser,
        hideMessage: orpc.admin.hideSingleMessage,
        unhideMessage: orpc.admin.unhideSingleMessage,
        deleteMessage: orpc.admin.deleteSingleMessage,
        gangInfo: orpc.admin.getGangInfo,
    },

    // User Routes
    user: {
        facultyList: orpc.user.getUserList,
        currentUserInfo: orpc.user.getCurrentUserInfo,
        userInfo: orpc.user.getUserInfo,
        inventory: orpc.user.getInventory,
        equippedItems: orpc.user.getEquippedItems,
        tradeableInventory: orpc.user.getTradeableInventory,
        statusEffects: orpc.user.getStatusEffects,
        updateProfileDetails: orpc.user.updateProfileDetails,
        train: orpc.user.train,
        equip: orpc.user.equipItem,
        unequip: orpc.user.unequipItem,
        useItem: orpc.user.useItem,
        gameConfig: orpc.user.getGameConfig,
        linkDiscord: orpc.user.linkDiscord,
        setLastNewsIdRead: orpc.user.setLastNewsIDRead,
        skills: orpc.user.getSkills,
    },

    // Profile Routes
    profile: {
        viewComments: orpc.profileComment.getComments,
        createComment: orpc.profileComment.postComment,
    },

    // Registration Codes Routes (ORPC)
    registrationCodes: {
        referralCodeList: orpc.auth.getReferralCodes,
        checkCode: orpc.auth.checkCode,
    },

    // Gang Routes
    gang: {
        gangList: orpc.gang.getGangList,
        gangInfo: orpc.gang.getGangInfo,
        createGang: orpc.gang.createGang,
        currentGangInfo: orpc.gang.getCurrentGang,
        memberShares: orpc.gang.getMemberShares,
        gangLogs: orpc.gang.getGangLogs,
        gangInviteList: orpc.gang.getInviteList,
        hasGangSigil: orpc.gang.hasGangSigil,
        gangInvite: orpc.gang.inviteMember,
        currentInvites: orpc.gang.getCurrentInvites,
        acceptInvite: orpc.gang.acceptInvite,
        declineInvite: orpc.gang.declineInvite,
        assignRank: orpc.gang.assignRank,
        updateShares: orpc.gang.updatePayoutShares,
        upgradeHideout: orpc.gang.upgradeHideout,
        leaveGang: orpc.gang.leaveGang,
        requestInvite: orpc.gang.requestInvite,
        updateGangInfo: orpc.gang.updateGangInfo,
        kickMember: orpc.gang.kickMember,
    },

    // Items Routes
    items: {
        getUpgradeItems: orpc.item.getUpgradeItems,
        upgradeItem: orpc.item.upgradeItem,
        getItemList: orpc.admin.item.list, // Dev only route
    },

    // Bank Routes
    bank: {
        withdraw: orpc.bank.withdraw,
        deposit: orpc.bank.deposit,
        transfer: orpc.bank.transfer,
        bankTransactions: orpc.bank.getBankTransactions,
    },

    // Shops Routes
    shops: {
        shopList: orpc.shop.shopList,
        shopInfo: orpc.shop.shopInfo,
        createShop: orpc.shop.admin.createShop,
        editShop: orpc.shop.admin.updateShop,
        deleteShop: orpc.shop.admin.deleteShop,
        createShopListing: orpc.shop.admin.createShopListing,
        editShopListing: orpc.shop.admin.editShopListing,
        deleteShopListing: orpc.shop.admin.deleteShopListing,
        purchaseItem: orpc.shop.purchaseItem,
        sellItem: orpc.shop.sellItem,
        getTraderRep: orpc.shop.getTraderRep,
    },

    // Job Routes
    jobs: {
        jobList: orpc.job.list,
        currentJobInfo: orpc.job.info,
        applyForJob: orpc.job.apply,
        applyForPromotion: orpc.job.promote,
        getJobLvlReqs: orpc.job.getRequirements,
        changePayoutTime: orpc.job.changePayoutTime,
    },

    battle: {
        battleBegin: orpc.battle.begin,
        attack: orpc.battle.attack,
        postBattleAction: orpc.battle.postBattleAction,
        status: orpc.battle.getStatus,
    },

    // Chat Routes
    chat: {
        history: orpc.chat.getHistory,
        rooms: orpc.chat.getRooms,
    },

    // Infirmary Routes
    infirmary: {
        infirmaryList: orpc.infirmary.getHospitalList,
        injuredList: orpc.infirmary.getInjuredList,
        revivePlayer: orpc.infirmary.revivePlayer,
        checkIn: orpc.infirmary.hospitalCheckIn,
    },

    // Jail Routes
    jail: {
        jailList: orpc.jail.jailList,
        jailBail: orpc.jail.bail,
    },

    // Property Routes
    property: {
        housingList: orpc.property.getHousingList,
        getUserProperties: orpc.property.getUserProperties,
        purchase: orpc.property.purchaseProperty,
        sell: orpc.property.sellProperty,
        setPrimary: orpc.property.setPrimaryProperty,
    },

    // Crafting Routes
    crafting: {
        getQueue: orpc.crafting.getCraftingQueue,
        recipeList: orpc.crafting.getRecipes,
        craftItem: orpc.crafting.craftItem,
        completeCraft: orpc.crafting.completeCraft,
        cancelCraft: orpc.crafting.cancelCraft,
    },

    // Private Messaging Routes
    messaging: {
        history: orpc.privateMessage.getChatHistory,
        unread: orpc.privateMessage.getUnreadCount,
        sendMessage: orpc.privateMessage.sendMessage,
        readMessage: orpc.privateMessage.markMessageRead,
    },

    // Roguelike Routes
    roguelike: {
        currentMap: orpc.roguelike.getCurrentMap,
        begin: orpc.roguelike.beginRun,
        advance: orpc.roguelike.advance,
        activateNode: orpc.roguelike.activateNode,
        scavengeOption: orpc.roguelike.chooseScavengeOption,
    },

    // Notifications Routes
    notifications: {
        history: orpc.notification.getList,
        unread: orpc.notification.getUnreadCount,
        read: orpc.notification.markRead,
        updatePushNotificationSettings: orpc.notification.updatePushSettings,
        saveFcmToken: orpc.notification.saveFCMToken,
    },

    // Leaderboards Routes
    leaderboards: {
        getBoards: orpc.leaderboard.getLeaderBoards,
        getEmoteBoards: orpc.leaderboard.getChatEmoteLeaderboards,
    },

    // Courses/Dojo Routes
    courses: {
        courseList: orpc.course.list,
        startCourse: orpc.course.start,
    },

    // Casino Routes
    casino: {
        slots: orpc.casino.gamble,
        lottery: orpc.casino.getLottery,
        enterLottery: orpc.casino.enterLottery,
        checkEntry: orpc.casino.checkLotteryEntry,
        rouletteBet: orpc.casino.placeBet,
    },

    // Special/Unique Items Routes
    specialItems: {
        deathNote: orpc.item.useDeathNote,
        lifeNote: orpc.item.useLifeNote,
        kompromat: orpc.item.useKompromat,
        megaphone: orpc.item.useMegaphone,
        dailyChest: orpc.item.useDailyChest,
        dailyChestItems: orpc.item.getDailyChestItems,
        redeemMaterialsCrate: orpc.item.useMaterialsCrate,
        redeemToolsCrate: orpc.item.useToolsCrate,
    },

    // Pets Routes
    pets: {
        list: orpc.pets.list,
        feed: orpc.pets.feed,
        play: orpc.pets.play,
        train: orpc.pets.train,
        customize: orpc.pets.customize,
        evolve: orpc.pets.evolve,
        setActive: orpc.pets.setActive,
    },

    // Talents Routes
    talents: {
        getTalents: orpc.talents.getTalents,
        getUnlockedTalents: orpc.talents.getUnlockedTalents,
        getEquippedAbilities: orpc.talents.getEquippedAbilities,
        unlockTalent: orpc.talents.unlockTalent,
        equipAbility: orpc.talents.equipAbility,
        unequipAbility: orpc.talents.unequipAbility,
        resetTalents: orpc.talents.resetTalents,
    },

    // Quests/Tasks Routes
    quests: {
        getQuestProgress: orpc.quest.getProgress,
        availableQuests: orpc.quest.getAvailable,
        startQuest: orpc.quest.start,
        completeQuest: orpc.quest.complete,
        getCombinedQuestList: orpc.quest.getCombinedList,
        activeQuests: orpc.quest.getActive,
        completedQuests: orpc.quest.getCompleted,
        handInItem: orpc.quest.handInItem,
    },

    // Bounty Routes
    bounties: {
        bountyList: orpc.bounty.getBountyList,
        activeBountyList: orpc.bounty.getActiveBountyList,
        placeBounty: orpc.bounty.placeBounty,
        deleteBounty: orpc.bounty.deleteBounty,
    },

    // Suggestions Routes
    suggestions: {
        suggestionList: orpc.suggestions.getSuggestions,
        voteHistory: orpc.suggestions.getVoteHistory,
        comments: orpc.suggestions.getComments,
        createSuggestion: orpc.suggestions.create,
        suggestionVote: orpc.suggestions.vote,
        suggestionComment: orpc.suggestions.comment,
        updateSuggestionState: orpc.suggestions.changeState,
        availablePolls: orpc.suggestions.getAvailablePolls,
        createPollResponse: orpc.suggestions.submitPollResponse,
        pollResults: orpc.suggestions.getPollResults,
    },

    missions: {
        missionList: orpc.mission.getList,
        startMission: orpc.mission.start,
        cancelMission: orpc.mission.cancel,
        currentMission: orpc.mission.getCurrent,
    },

    shrine: {
        dailyGoal: orpc.shrine.getGoal,
        getDonations: orpc.shrine.getDonations,
        donate: orpc.shrine.donate,
        activeShrineBuffer: orpc.shrine.getActiveBuff,
        isBuffActive: orpc.shrine.isBuffActive,
    },

    auctions: {
        auctionList: orpc.auction.getList,
        createAuctionListing: orpc.auction.createListing,
        buyoutListing: orpc.auction.buyoutListing,
        cancelListing: orpc.auction.cancelListing,
    },

    rooftop: {
        npcList: orpc.battle.rooftopList,
        begin: orpc.battle.beginRooftopBattle,
    },

    social: {
        friendList: orpc.social.getFriends,
        friendRequests: orpc.social.getFriendRequests,
        sendFriendRequest: orpc.social.sendFriendRequest,
        respondFriendRequest: orpc.social.respondToFriendRequest,
        removeFriend: orpc.social.removeFriend,
        updateFriendNote: orpc.social.updateFriendNote,
        updateStatusMessage: orpc.social.updateStatusMessage,
        togglePrivacySettings: orpc.social.updatePrivacySettings,
        getRivalList: orpc.social.getRivals,
        addRival: orpc.social.addRival,
        removeRival: orpc.social.removeRival,
        updateRivalNote: orpc.social.updateRivalNote,
    },

    story: {
        getSeasons: orpc.story.getSeasons,
        makeChoice: orpc.story.makeChoice,
        completeEpisode: orpc.story.completeEpisode,
    },

    scavenging: {
        generateGrid: orpc.skills.scavenging.generateGrid,
        revealCell: orpc.skills.scavenging.revealCell,
        activeSession: orpc.skills.scavenging.getActiveSession,
        devGrid: orpc.skills.scavenging.devGrid,
        endSession: orpc.skills.scavenging.endSession,
        resetGrid: orpc.skills.scavenging.resetGrid,
    },

    mining: {
        start: orpc.skills.startMining,
        processSwing: orpc.skills.processSwing,
        session: orpc.skills.getMiningSession,
        cancel: orpc.skills.cancelMining,
    },

    // Explore Routes
    explore: {
        map: orpc.explore.getMapByLocation,
        interact: orpc.explore.interactWithNode,
    },
};
