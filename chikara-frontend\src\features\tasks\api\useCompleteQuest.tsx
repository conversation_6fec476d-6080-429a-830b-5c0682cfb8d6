import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export function useCompleteQuest() {
    const queryClient = useQueryClient();

    return useMutation(
        api.quests.completeQuest.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.quests.completedQuests.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.quests.activeQuests.key(),
                });
                queryClient.invalidateQueries({ queryKey: api.user.currentUserInfo.key() });
            },
            onError: (error) => {
                console.error("Failed to complete quest:", error);
            },
        })
    );
}
