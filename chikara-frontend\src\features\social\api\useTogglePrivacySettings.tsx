import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

// interface TogglePrivacySettingsParams {
//     showLastOnline: boolean;
// }

const useTogglePrivacySettings = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.togglePrivacySettings.mutationOptions({
            onSuccess: () => {
                toast.success("Privacy settings updated!");
                // Invalidate current user info to refresh with new settings
                queryClient.invalidateQueries({ queryKey: api.user.currentUserInfo.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to update privacy settings");
            },
        })
    );
};

export default useTogglePrivacySettings;
